# 🔧 Guide d'Activation de l'API Google Photos

## 🚨 Problème Identifié

**Erreur 401 (Forbidden)** lors de l'accès à Google Photos :
```
POST https://photoslibrary.googleapis.com/v1/mediaItems:search 401 (Forbidden)
```

## 🎯 Cause Principale

L'API Google Photos n'est **pas activée** dans votre projet Google Cloud Console.

## ✅ Solution : Activer l'API Google Photos

### Étape 1 : Accéder à Google Cloud Console

1. Allez sur : https://console.cloud.google.com/
2. Sélectionnez votre projet (celui avec le Client ID configuré)

### Étape 2 : Activer l'API Photos Library

1. **Naviguez vers "APIs & Services" > "Library"**
2. **Recherchez "Photos Library API"**
3. **Cliquez sur "Photos Library API"**
4. **Cliquez sur "ENABLE" (Activer)**

**URL directe :** https://console.cloud.google.com/apis/library/photoslibrary.googleapis.com

### Étape 3 : Vérifier l'Activation

1. Allez dans "APIs & Services" > "Enabled APIs"
2. Vérifiez que "Photos Library API" apparaît dans la liste

## 🔍 Debugging Ajouté

Le service a été amélioré avec :

### Vérification des Permissions
```typescript
// Nouvelle méthode pour vérifier les permissions
static async checkTokenPermissions(): Promise<void>
```

### Messages d'Erreur Spécifiques
- ✅ Détection automatique de l'erreur 401
- ✅ Message explicite pour activer l'API
- ✅ URL directe vers la configuration

### Logs de Debugging
```
🔍 Scope demandé: https://www.googleapis.com/auth/photoslibrary.readonly
🔑 Token (premiers caractères): ya29.a0AcM612x...
🔍 Informations du token: {...}
📋 Scopes autorisés: https://www.googleapis.com/auth/photoslibrary.readonly
```

## 🧪 Test Après Activation

1. **Activez l'API** dans Google Cloud Console
2. **Rechargez l'application** (F5)
3. **Reconnectez-vous** à Google Photos
4. **Vérifiez les logs** dans la console

### Logs Attendus (Succès)
```
✅ Permissions Google Photos accordées
✅ X photos récentes récupérées
```

### Logs d'Erreur (Si problème persiste)
```
❌ Permissions Google Photos manquantes
🔧 Scopes disponibles: [liste des scopes]
```

## 📋 Checklist de Vérification

- [ ] API Photos Library activée dans Google Cloud Console
- [ ] Client OAuth 2.0 configuré avec les bonnes origines
- [ ] Variables d'environnement correctes (.env.local)
- [ ] Scope `photoslibrary.readonly` dans la configuration
- [ ] Test de connexion réussi

## 🔗 Liens Utiles

- **Google Cloud Console :** https://console.cloud.google.com/
- **API Photos Library :** https://console.cloud.google.com/apis/library/photoslibrary.googleapis.com
- **Documentation API :** https://developers.google.com/photos/library/guides/get-started
