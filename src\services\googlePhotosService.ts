import { auth } from './api';

// Interface pour les photos Google Photos
export interface GooglePhoto {
  id: string;
  baseUrl: string;
  filename: string;
  mediaMetadata: {
    creationTime: string;
    width: string;
    height: string;
  };
  mimeType: string;
}

// Interface pour la réponse de l'API Google Photos
interface GooglePhotosResponse {
  mediaItems: GooglePhoto[];
  nextPageToken?: string;
}

// Configuration Google API depuis les variables d'environnement
const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
const SCOPES = import.meta.env.VITE_GOOGLE_PHOTOS_SCOPE || 'https://www.googleapis.com/auth/photoslibrary.readonly';

// Déclaration des types pour l'API Google
declare global {
  interface Window {
    gapi: any;
    google: any;
  }
}

/**
 * Service pour interagir avec l'API Google Photos
 * Utilise la nouvelle Google Identity Services API
 */
export class GooglePhotosService {
  private static readonly BASE_URL = 'https://photoslibrary.googleapis.com/v1';
  private static isInitialized = false;
  private static accessToken: string | null = null;
  private static tokenClient: any = null;

  /**
   * Initialiser l'API Google Photos avec la nouvelle Google Identity Services
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Vérifier que les clés API sont configurées
    const apiKey = import.meta.env.VITE_GOOGLE_API_KEY;
    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

    if (!apiKey || !clientId) {
      throw new Error('Clés API Google non configurées. Vérifiez votre fichier .env.local');
    }

    console.log('🔑 Initialisation Google Photos API (nouvelle version)...');
    console.log('📸 Client ID:', clientId.substring(0, 20) + '...');

    try {
      // Charger les scripts Google nécessaires
      await this.loadGoogleScripts();

      // Initialiser le client GAPI
      await this.initGapiClient();

      // Initialiser le client d'authentification
      this.initTokenClient();

      this.isInitialized = true;
      console.log('✅ Google Photos API initialisé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error);
      throw error;
    }
  }

  /**
   * Charger les scripts Google nécessaires
   */
  private static async loadGoogleScripts(): Promise<void> {
    // Charger Google API
    if (!window.gapi) {
      await this.loadScript('https://apis.google.com/js/api.js');
      console.log('✅ Script Google API chargé');
    }

    // Charger Google Identity Services
    if (!window.google) {
      await this.loadScript('https://accounts.google.com/gsi/client');
      console.log('✅ Script Google Identity Services chargé');
    }
  }

  /**
   * Charger un script de manière asynchrone
   */
  private static loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Impossible de charger ${src}`));
      document.head.appendChild(script);
    });
  }

  /**
   * Initialiser le client GAPI pour l'API Photos
   */
  private static async initGapiClient(): Promise<void> {
    return new Promise((resolve, reject) => {
      window.gapi.load('client', async () => {
        try {
          const apiKey = import.meta.env.VITE_GOOGLE_API_KEY;

          await window.gapi.client.init({
            apiKey: apiKey,
            discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/photoslibrary/v1/rest'],
          });

          console.log('✅ Client GAPI initialisé');
          resolve();
        } catch (error) {
          console.error('❌ Erreur d\'initialisation GAPI:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Initialiser le client d'authentification avec Google Identity Services
   */
  private static initTokenClient(): void {
    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
    const scope = import.meta.env.VITE_GOOGLE_PHOTOS_SCOPE || 'https://www.googleapis.com/auth/photoslibrary.readonly';

    this.tokenClient = window.google.accounts.oauth2.initTokenClient({
      client_id: clientId,
      scope: scope,
      callback: (response: any) => {
        if (response.error) {
          console.error('❌ Erreur d\'authentification:', response.error);
          return;
        }

        this.accessToken = response.access_token;
        console.log('✅ Token d\'accès obtenu');
        console.log('🔍 Scope demandé:', scope);
        console.log('🔑 Token (premiers caractères):', response.access_token.substring(0, 20) + '...');
      },
    });

    console.log('✅ Client d\'authentification initialisé');
  }

  /**
   * Vérifier si l'utilisateur est connecté à Google Photos
   */
  static async isSignedIn(): Promise<boolean> {
    await this.initialize();
    return this.accessToken !== null;
  }

  /**
   * Vérifier les permissions du token d'accès
   */
  static async checkTokenPermissions(): Promise<void> {
    if (!this.accessToken) {
      console.log('❌ Aucun token d\'accès disponible');
      return;
    }

    try {
      // Vérifier les informations du token
      const response = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${this.accessToken}`);
      const tokenInfo = await response.json();

      console.log('🔍 Informations du token:', tokenInfo);
      console.log('📋 Scopes autorisés:', tokenInfo.scope);

      if (tokenInfo.scope && tokenInfo.scope.includes('photoslibrary')) {
        console.log('✅ Permissions Google Photos accordées');
      } else {
        console.log('❌ Permissions Google Photos manquantes');
        console.log('🔧 Scopes disponibles:', tokenInfo.scope);
      }
    } catch (error) {
      console.error('❌ Erreur lors de la vérification du token:', error);
    }
  }

  /**
   * Connecter l'utilisateur à Google Photos
   */
  static async signIn(): Promise<void> {
    try {
      await this.initialize();

      if (!this.tokenClient) {
        throw new Error('Client d\'authentification non initialisé');
      }

      console.log('🔐 Tentative de connexion à Google Photos...');

      // Vérifier si déjà connecté
      if (this.accessToken) {
        console.log('✅ Utilisateur déjà connecté à Google Photos');
        return;
      }

      // Demander l'autorisation avec la nouvelle API
      return new Promise((resolve, reject) => {
        // Configurer le callback temporaire
        const originalCallback = this.tokenClient.callback;

        this.tokenClient.callback = (response: any) => {
          // Restaurer le callback original
          this.tokenClient.callback = originalCallback;

          if (response.error) {
            console.error('❌ Erreur d\'authentification:', response.error);
            reject(new Error(`Connexion échouée: ${response.error}`));
            return;
          }

          this.accessToken = response.access_token;
          console.log('✅ Connexion à Google Photos réussie');
          resolve();
        };

        // Déclencher la demande d'autorisation
        this.tokenClient.requestAccessToken({ prompt: 'consent' });
      });

    } catch (error) {
      console.error('❌ Erreur lors de la connexion à Google Photos:', error);
      throw new Error(`Connexion échouée: ${error.message || 'Erreur inconnue'}`);
    }
  }

  /**
   * Obtenir le token d'accès Google OAuth pour les Photos
   */
  private static async getGoogleAccessToken(): Promise<string> {
    await this.initialize();

    if (!this.authInstance?.isSignedIn.get()) {
      await this.signIn();
    }

    const user = this.authInstance.currentUser.get();
    const authResponse = user.getAuthResponse();

    if (!authResponse.access_token) {
      throw new Error('Token d\'accès non disponible');
    }

    return authResponse.access_token;
  }

  /**
   * Récupérer les photos récentes (dernières 24h par défaut)
   */
  static async getRecentPhotos(pageSize: number = 20, hoursBack: number = 24): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!this.accessToken) {
        await this.signIn();
      }

      // Vérifier les permissions du token
      await this.checkTokenPermissions();

      console.log('🔍 Tentative de récupération des photos...');
      console.log('🔑 Token utilisé:', this.accessToken ? 'Présent' : 'Absent');

      // Essayer d'abord une requête simple sans filtres
      let response;
      try {
        console.log('📡 Recherche des photos "Aujourd\'hui"...');

        // Créer un filtre pour les photos d'aujourd'hui
        const today = new Date();
        const searchBody = {
          pageSize: Math.min(pageSize, 10),
          filters: {
            dateFilter: {
              ranges: [{
                startDate: {
                  year: today.getFullYear(),
                  month: today.getMonth() + 1,
                  day: today.getDate()
                },
                endDate: {
                  year: today.getFullYear(),
                  month: today.getMonth() + 1,
                  day: today.getDate()
                }
              }]
            },
            mediaTypeFilter: {
              mediaTypes: ['PHOTO']
            }
          }
        };

        console.log('📅 Recherche pour la date:', {
          year: today.getFullYear(),
          month: today.getMonth() + 1,
          day: today.getDate()
        });

        response = await fetch(`${this.BASE_URL}/mediaItems:search`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(searchBody)
        });
      } catch (error) {
        console.error('❌ Erreur de réseau:', error);
        throw new Error(`Erreur de réseau: ${error.message}`);
      }

      if (!response.ok) {
        if (response.status === 401) {
          console.error('❌ Erreur 401: Token d\'accès invalide ou permissions insuffisantes');
          console.error('🔧 Vérifiez que l\'API Google Photos est activée dans Google Cloud Console');
          console.error('🔧 URL: https://console.cloud.google.com/apis/library/photoslibrary.googleapis.com');
          throw new Error('Accès refusé à Google Photos. Vérifiez que l\'API est activée dans Google Cloud Console.');
        }

        if (response.status === 403) {
          console.error('❌ Erreur 403: Permissions insuffisantes pour accéder à Google Photos');
          console.error('🔧 Causes possibles:');
          console.error('   1. Scope insuffisant (besoin de photoslibrary.readonly)');
          console.error('   2. API Google Photos non activée pour ce projet');
          console.error('   3. Quota API dépassé');
          console.error('   4. Restrictions sur le compte Google');

          // Récupérer plus de détails sur l'erreur
          try {
            const errorDetails = await response.json();
            console.error('🔍 Détails de l\'erreur 403:', errorDetails);
          } catch (e) {
            console.error('🔍 Impossible de récupérer les détails de l\'erreur');
          }

          throw new Error('Permissions insuffisantes pour accéder à Google Photos. Vérifiez les scopes et l\'activation de l\'API.');
        }

        throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const mediaItems = data.mediaItems || [];

      // Convertir au format de notre interface
      const photos: GooglePhoto[] = mediaItems.map((item: any) => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

      console.log(`✅ ${photos.length} photos récentes récupérées`);
      return photos;

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos Google:', error);
      throw error;
    }
  }





  /**
   * Convertir une photo Google Photos en File pour l'upload
   */
  static async downloadPhotoAsFile(photo: GooglePhoto): Promise<File> {
    try {
      // Ajouter les paramètres de taille pour télécharger la photo en taille originale
      const downloadUrl = `${photo.baseUrl}=d`;

      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`Erreur de téléchargement: ${response.status}`);
      }

      const blob = await response.blob();

      // Créer un objet File à partir du blob
      const file = new File([blob], photo.filename, {
        type: photo.mimeType,
        lastModified: new Date(photo.mediaMetadata.creationTime).getTime()
      });

      return file;

    } catch (error) {
      console.error('Erreur lors du téléchargement de la photo:', error);
      throw new Error('Impossible de télécharger la photo');
    }
  }

  /**
   * Vérifier si l'utilisateur a accordé les permissions Google Photos
   */
  static async hasPhotosPermission(): Promise<boolean> {
    try {
      await this.initialize();

      // Vérifier directement si l'utilisateur est connecté à l'API Google Photos
      return await this.isSignedIn();
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error);
      return false;
    }
  }

  /**
   * Récupérer toutes les photos (sans filtre de date)
   */
  static async getAllPhotos(pageSize: number = 50): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!this.accessToken) {
        await this.signIn();
      }

      // Utiliser l'API REST directement
      const response = await fetch(`${this.BASE_URL}/mediaItems`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const mediaItems = data.mediaItems || [];

      const photos: GooglePhoto[] = mediaItems.map((item: any) => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

      console.log(`✅ ${photos.length} photos récupérées`);
      return photos;

    } catch (error) {
      console.error('❌ Erreur lors de la récupération de toutes les photos:', error);
      throw error;
    }
  }

  /**
   * Récupérer les photos par type (plantes, jardinage, etc.)
   */
  static async getPhotosByType(searchTerms: string[] = ['plant', 'garden', 'flower'], pageSize: number = 30): Promise<GooglePhoto[]> {
    try {
      await this.initialize();

      if (!this.authInstance) {
        throw new Error('Service Google Photos non initialisé');
      }

      if (!await this.isSignedIn()) {
        await this.signIn();
      }

      // Note: L'API Google Photos ne permet pas de recherche par contenu directement
      // On récupère toutes les photos et on peut filtrer côté client si nécessaire
      const allPhotos = await this.getAllPhotos(pageSize);

      // Pour l'instant, on retourne toutes les photos
      // Dans une version future, on pourrait utiliser l'API Vision pour filtrer
      return allPhotos;

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos par type:', error);
      throw error;
    }
  }
}
